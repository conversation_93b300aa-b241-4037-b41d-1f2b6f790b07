<template>
    <div class="example-container">
        <demand-list
            :key="demandListKey"
            ref="demandList"
            :rowKey="getRowKey"
            v-model="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :data="treeData"
            :actions-width="100"
            :default-search-open="false"
            :total="total"
            :page.sync="currentPage"
            :limit.sync="pageSize"
            :selectable="checkRowSelectable"
            :enable-lazy-load="true"
            :load-children="loadDemandChildren"
            :table-attrs="{ height: 'calc(100vh - 190px)' }"
            @pagination="query"
            @search="handleSearch"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @sort-change="handleSortChange"
            @selection-change="handleSelectionChange"
        >
            <template #rightNav>
                <el-button type="text" @click="batchClose" style="margin-right: 10px"> 批量关闭 </el-button>
            </template>
            <template #demandName="{ row }">
                <div class="demand-name-wrapper">
                    <el-tooltip :content="row.demandName" placement="top" :disabled="!row.demandName">
                        <div class="demand-name-container">
                            <svg-icon :icon-class="computedDemandNameIcon(row)" class="svg-icon"></svg-icon>
                            <span class="demand-name-text">{{ row.demandName }}</span>
                            <span class="demand-name-suffix" v-if="row.demandClass !== '产品需求'">
                                <svg-icon
                                    icon-class="dms-demand-tree-numbers"
                                    class="svg-icon"
                                    style="width: 10px"
                                ></svg-icon>
                                {{ row.finishCount || 0 }}/{{ row.allCount || 0 }}
                            </span>
                        </div>
                    </el-tooltip>
                </div>
            </template>
            <!-- 操作列 -->
            <template #actions="{ row }">
                <el-button type="text" @click="getDetail(row)" v-permission="['kpiFunction']">详情1</el-button>
                <!-- 历史版本只能查看 -->
                <template v-if="!row.historyVersionFlag">
                    <el-button
                        v-if="row.demandClass === '原始需求' && row.checkStatus === '待审核'"
                        type="text"
                        @click="handleChangeCheck(row)"
                        >审核</el-button
                    >
                    <!-- 只有子原始需求才能编辑 -->
                    <el-button
                        v-if="
                            row.demandClass === '子原始需求' &&
                            (row.demandStatus === '未开始' ||
                                row.demandStatus === '已计划' ||
                                row.demandStatus === '进行中')
                        "
                        type="text"
                        @click="handleEdit(row)"
                        >编辑</el-button
                    >
                    <!-- 只有原始需求才能拆分 -->
                    <el-button
                        v-if="
                            row.demandClass === '原始需求' &&
                            (row.demandStatus === '未开始' ||
                                row.demandStatus === '已计划' ||
                                row.demandStatus === '进行中')
                        "
                        type="text"
                        @click="handleSplit(row)"
                        >拆分</el-button
                    >
                    <el-button v-if="row.demandStatus === '已完成'" type="text" @click="handleClose(row)"
                        >关闭</el-button
                    >
                </template>
            </template>
        </demand-list>
        <!-- 详情弹窗 -->
        <OriginalDemand :visible.sync="originalDemandDialogVisible" :demandId="currentDemandId"></OriginalDemand>
        <SubOriginalDemand
            :visible.sync="subOriginalDemandDialogVisible"
            :demandId="currentDemandId"
        ></SubOriginalDemand>
        <ProductDemand :visible.sync="productDemandDialogVisible" :demandId="currentDemandId"></ProductDemand>
        <UserDemand :visible.sync="userDemandDialogVisible" :demandId="currentDemandId"></UserDemand>
        <!-- 需求内容变更确认弹窗 -->
        <ClaimingDialog
            :visible.sync="claimingDialogVisible"
            :demandName="currentDemandName"
            :proposal="currentProposer"
            :rowData="currentRow"
            :demandId="currentDemandId"
            @success="query"
        ></ClaimingDialog>
        <!-- 拆分需求弹窗 -->
        <DecompositionDialog
            :visible.sync="decompositionDialogVisible"
            :demandId="currentDemandId"
            :rowData="currentRow"
            @success="query"
        ></DecompositionDialog>
        <!-- 变更审核弹窗 -->
        <ReviewChange
            :visible.sync="reviewChangeVisible"
            :demandId="currentDemandId"
            :rowData="currentRow"
            @success="query"
        ></ReviewChange>
        <!-- 编辑弹窗 -->
        <EditSubDemandDialog
            :visible.sync="editDemandDialogVisible"
            :demandId="currentDemandId"
            :rowData="currentRow"
            @success="query"
        ></EditSubDemandDialog>
        <!-- 关闭需求弹窗 -->
        <CloseDialog
            :visible.sync="closeDialogVisible"
            :demandId="currentDemandId"
            :rowData="currentRow"
            @success="query"
            @view-detail="handleViewDetailFromClose"
            :isMultiple="isMultiple"
            :rowDataList="selectedRows"
        ></CloseDialog>
    </div>
</template>

<script>
import DemandList from 'dms/views/demandManagement/components/demandList/index.vue';
import { queryParams, queryConfig, navItems } from './formInit.js';
import OriginalDemand from 'dms/views/demandManagement/demandDetail/OriginalDemand.vue';
import SubOriginalDemand from 'dms/views/demandManagement/demandDetail/SubOriginalDemand.vue';
import ProductDemand from 'dms/views/demandManagement/demandDetail/ProductDemand.vue';
import UserDemand from 'dms/views/demandManagement/demandDetail/UserDemand.vue';
import DecompositionDialog from './components/DecompositionDialog.vue';
import ClaimingDialog from './components/ClaimingDialog.vue';
import ReviewChange from './components/ReviewChange.vue';
import EditSubDemandDialog from './components/EditSubDemandDialog.vue';
import CloseDialog from 'dms/views/demandManagement/components/CloseDialog.vue';
import { v4 as uuidv4 } from 'uuid';

export default {
    name: 'AduitDemand',
    components: {
        DemandList,
        OriginalDemand,
        SubOriginalDemand,
        ProductDemand,
        UserDemand,
        DecompositionDialog,
        ReviewChange,
        ClaimingDialog,
        EditSubDemandDialog,
        CloseDialog
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '待审核',
            // 顶部查询栏配置
            navItems,
            // 查询参数
            params: { checkStatus: '待审核' },
            // 选中的行数据
            selectedRows: [],
            // 查询配置
            queryConfig,
            // 查询参数
            queryParams,
            // 表格数据
            treeData: [],
            // 当前行数据
            currentRow: {},
            // 默认创建时间倒序
            sortOrder: 'descending',
            // 默认创建时间倒序
            sortKey: 'proposalStartTime',
            demandListKey: 0,
            currentPage: 1,
            pageSize: 10,
            total: 100,
            // 原始需求弹窗
            originalDemandDialogVisible: false,
            // 子原始需求弹窗
            subOriginalDemandDialogVisible: false,
            // 产品需求弹窗
            productDemandDialogVisible: false,
            // 用户需求弹窗
            userDemandDialogVisible: false,
            // 审核弹窗--认领需求
            claimingDialogVisible: false,
            // 拆分弹窗
            decompositionDialogVisible: false,
            // 变更审核弹窗
            reviewChangeVisible: false,
            // 编辑弹窗
            editDemandDialogVisible: false,
            // 关闭需求弹窗
            closeDialogVisible: false,
            // 当前选中的需求
            currentDemandName: '',
            // 当前选中的提出人
            currentProposer: '',
            // 当前选中的需求ID
            currentDemandId: '',
            // 是否选中多个需求
            isMultiple: false
        };
    },
    watch: {
        'queryParams.productLine': {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.getProductOptions(newVal);
                }
            }
        }
    },
    created() {
        this.getProjectOrGroup();
        this.query();
    },
    methods: {
        /**
         * 获取行的唯一标识
         * @param {Object} row - 行数据
         * @returns {String} 行的唯一标识
         */
        getRowKey(row) {
            return row.id;
        },
        /**
         * 获取项目或团队
         */
        async getProjectOrGroup() {
            const api = this.$service.dms.common.getProjectOrGroupList;
            const params = {};
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.queryConfig.items.find((i) => i.modelKey === 'assProjectId').elOptions = res.data.map((i) => ({
                    label: i.projectName,
                    value: i.projectId
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取产品下拉选项
         * @param {String} productLine  产品线
         */
        async getProductOptions(productLine) {
            this.queryParams.assProductId = '';
            try {
                const params = {
                    statusList: ['进行中'],
                    productLine
                };
                const api = this.$service.dms.common.getProductList;
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.queryConfig.items.find((i) => i.modelKey === 'assProductId').elOptions = res.data.map((i) => ({
                    label: i.productName,
                    value: i.ztProductId
                }));
            } catch (error) {
                console.error('获取产品选项失败:', error);
            }
        },
        /**
         * 查询
         */
        async query() {
            try {
                const params = {
                    ...this.params,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder,
                    // 此项必填
                    storyClass: '原始需求',
                    currentPage: this.currentPage,
                    pageSize: this.pageSize
                };
                const api = this.$service.dms.demand.getAuditDemandList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }

                // 处理数据，为懒加载准备
                this.treeData = this.processTreeData(res.data.list || []);
                this.total = res.data.total || 0;
                // 无法修改内部子节点懒加载状态，被迫使用key更新整个列表
                this.demandListKey += 1;
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },

        /**
         * 处理树状数据，为懒加载做准备
         * @param {Array} data - 原始数据
         * @returns {Array} 处理后的数据
         */
        processTreeData(data) {
            return data.map((item) => {
                const processedItem = { ...item, id: `${uuidv4()}_${item.demandId}` };

                // 如果有子节点，设置hasChildren为true，并清空children（懒加载时再获取）
                if (item.allCount && item.allCount > 0) {
                    processedItem.hasChildren = true;
                } else {
                    processedItem.hasChildren = false;
                }

                // 确保children为空数组，这样展开箭头会显示但没有子数据
                processedItem.children = [];
                return processedItem;
            });
        },

        /**
         * 懒加载子节点数据
         * @param {Object} tree - 当前节点数据
         * @param {Object} treeNode - 树节点对象
         * @returns {Promise<Array>} 子节点数据
         */
        async loadDemandChildren(tree, treeNode) {
            try {
                const params = {
                    demandId: tree.demandId,
                    storyClass: this.getNextDemandClass(tree.demandClass),
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder
                };

                // 调用API获取子节点数据
                const api = this.$service.dms.demand.getDemandChildren;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return [];
                }

                // 递归处理子节点数据
                return this.processTreeData(res.data || []);
            } catch (error) {
                console.error('加载子节点数据失败:', error);
                return [];
            }
        },

        /**
         * 获取下一个级别的需求
         * @param {String} currentDemand 当前需求
         * @returns {String} 需求
         */
        getNextDemandClass(currentDemand) {
            if (currentDemand === '原始需求') {
                return '子原始需求';
            } else if (currentDemand === '子原始需求') {
                return '用户需求';
            }
            return '产品需求';
        },

        /**
         * 处理搜索
         * @param {Object} searchData - 搜索表单数据
         */
        handleSearch(searchData) {
            // 调整navTab
            this.activeNavTab = '';
            const { searchParams } = searchData;
            const { proposalTime, expectedDate, costDeviation, estimateHour, actualHour, responsiblePerson } =
                searchParams;
            this.params = {
                ...searchParams,
                // 需求提出/创建开始日期
                proposalStartTime: proposalTime[0] || '',
                // 需求提出/创建结束日期
                proposalEndTime: proposalTime[1] || '',
                // 期望交付开始日期
                expectedStartDate: expectedDate[0] || '',
                // 期望交付结束日期
                expectedEndDate: expectedDate[1] || '',
                // 成本起始偏差
                costStartDeviation: costDeviation[0] || '',
                // 成本结束偏差
                costEndDeviation: costDeviation[1] || '',
                // 预估开始工时
                estimateStartHour: estimateHour[0] || '',
                // 预估结束工时
                estimateEndHour: estimateHour[1] || '',
                // 实际开始工时
                actualStartHour: actualHour[0] || '',
                // 实际结束工时
                actualEndHour: actualHour[1] || '',
                // 提出人前后不加逗号，需求负责人要加
                responsiblePerson: responsiblePerson ? `,${responsiblePerson},` : ''
            };
            this.query();
        },
        /**
         * 处理重置
         */
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
        },

        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.params = {
                [queryField]: field
            };
            this.query();
        },

        /**
         * 处理排序变化
         * @param {Object} sortInfo - 排序信息 { column, prop, order }
         */
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            this.sortKey = prop;
            this.sortOrder = order;
            this.query();
        },

        /**
         * 详情弹窗
         * @param {Object} row - 行数据
         */
        getDetail(row) {
            this.currentDemandId = row.demandId;
            if (row.demandClass === '原始需求') {
                this.originalDemandDialogVisible = true;
                return;
            } else if (row.demandClass === '子原始需求') {
                this.subOriginalDemandDialogVisible = true;
                return;
            } else if (row.demandClass === '用户需求') {
                this.userDemandDialogVisible = true;
                return;
            }
            this.productDemandDialogVisible = true;
            this.currentDemandId = row.demandId;
            if (row.demandClass === '原始需求') {
                this.originalDemandDialogVisible = true;
                return;
            } else if (row.demandClass === '子原始需求') {
                this.subOriginalDemandDialogVisible = true;
                return;
            } else if (row.demandClass === '用户需求') {
                this.userDemandDialogVisible = true;
                return;
            }
            this.productDemandDialogVisible = true;
        },
        /**
         * 选择项变化
         * @param {Array} selections 选中的行数据
         */
        handleSelectionChange(selections) {
            this.selectedRows = selections;
        },
        /**
         * 计算需求名称图标
         * @param {Object} row 行数据
         * @return {String} 图标名称
         */
        computedDemandNameIcon(row) {
            if (row.demandClass === '原始需求') {
                return 'dms-demand-original-demand';
            } else if (row.demandClass === '子原始需求') {
                return 'dms-demand-sub-original-demand';
            } else if (row.demandClass === '用户需求') {
                return 'dms-demand-user-demand';
            }
            return 'dms-demand-product-demand';
        },
        handleSplit(row) {
            this.currentDemandId = row.demandId;
            this.currentRow = row;
            this.decompositionDialogVisible = true;
        },
        handleClose(row) {
            this.isMultiple = false;
            this.currentDemandId = row.demandId;
            this.currentRow = row;
            this.closeDialogVisible = true;
        },
        batchClose() {
            if (this.selectedRows.length === 0) {
                this.$message.warning('请先选择需求');
                return;
            }
            this.isMultiple = true;
            this.closeDialogVisible = true;
        },
        /**
         * 需求审核分两种：1.内容变更/负责人变更（需求审核） 2.变更审核
         * @param {Object} row 行数据
         */
        handleChangeCheck(row) {
            this.currentDemandId = row.demandId;
            this.currentRow = row;
            if (row.checkType === '内容变更' || row.checkType === '负责人变更') {
                this.reviewChangeVisible = true;
            } else {
                this.currentDemandName = row.demandName;
                this.currentProposer = row.proposerName;
                this.claimingDialogVisible = true;
            }
        },
        handleEdit(row) {
            this.currentDemandId = row.demandId;
            this.currentRow = row;
            this.editDemandDialogVisible = true;
        },
        /**
         * 从关闭弹窗查看需求详情
         * @param {Object} data - 包含demandId和demandClass的对象
         */
        handleViewDetailFromClose(data) {
            const { demandId } = data;
            this.currentDemandId = demandId;
            this.originalDemandDialogVisible = true;
        },
        /**
         * 检查行是否可选择
         * @param {Object} row 行数据
         * @param {Number} index 行索引
         * @return {Boolean} 是否可选择
         */
        checkRowSelectable(row, index) {
            if (row.demandStatus === '已关闭' || row.historyVersionFlag) {
                return false;
            }
            return true;
        }
    }
};
</script>

<style lang="scss" scoped>
.example-container {
    padding: 20px;
}
.svg-icon {
    padding: 0;
}

.demand-name-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 32px; // 确保最小高度，避免换行
}

.demand-name-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    overflow: hidden;
    flex: 1;

    .demand-name-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 40px;
    }

    .demand-name-suffix {
        font-size: 10px;
        position: absolute;
        right: 0;
        color: #999;
        flex-shrink: 0;
        white-space: nowrap; // 防止数字换行
    }
}

// 确保树状表格的展开箭头和内容在同一行
::v-deep .el-table__row {
    .el-table__expand-column {
        .el-table__expand-icon {
            display: inline-flex;
            align-items: center;
            vertical-align: middle;
        }
    }
}

// 只对包含需求名称的列应用 flex 布局，避免影响其他列的 showOverflowTooltip
::v-deep td .cell:has(.demand-name-wrapper) {
    display: flex;
    align-items: center;
    line-height: 1.5;
    min-height: 32px;
}
</style>
